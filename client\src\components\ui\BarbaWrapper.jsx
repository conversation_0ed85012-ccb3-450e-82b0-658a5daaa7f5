"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link"; // Import Link for proper handling

export default function BarbaWrapper({ children }) {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Set mounted to true after the component is mounted in the browser
    setMounted(true);

    // Dynamically import Barba.js and GSAP only on the client side
    const setupBarba = async () => {
      // Dynamic imports to avoid SSR issues
      const barba = (await import("@barba/core")).default;
      const gsap = (await import("gsap")).default;

      // Create styles for the overlay
      const style = document.createElement("style");
      style.setAttribute("data-barba-styles", "");
      style.textContent = `
        .page-transition-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: var(--primary, #D46A6A);
          z-index: 9999;
          transform: scaleY(0);
          transform-origin: top center;
          pointer-events: none;
        }
        
        /* Loading indicator */
        .page-loader {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 10000;
          width: 40px;
          height: 40px;
          pointer-events: none;
          opacity: 0;
        }
        
        .page-loader:after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 3px solid rgba(255, 255, 255, 0.3);
          border-top-color: #fff;
          animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
      `;
      document.head.appendChild(style);
      document.addEventListener("barba:click", (e) => {
        const href = e.detail.href;
        barba.go(href);
      });

      try {
        // Initialize barba with Next.js integration
        barba.init({
          // Critical: Prevent default behavior of global triggers
          preventRunning: true,

          // Use this to completely disable default history management
          // since Next.js will handle that
          cacheIgnore: true,

          // This tells barba to use "prefetch" mode which doesn't change the URL
          // Next.js will handle URL changes
          prefetchIgnore: false,

          // Important for Next.js: Use full URLs for transitions but don't navigate automatically
          useTransitionLinks: false,

          transitions: [
            {
              name: "slide-transition",

              // Before leaving current page
              async leave(data) {
                const done = this.async();

                // Create the overlay if it doesn't exist
                let overlay = document.querySelector(
                  ".page-transition-overlay"
                );
                if (!overlay) {
                  overlay = document.createElement("div");
                  overlay.className = "page-transition-overlay";
                  document.body.appendChild(overlay);
                }

                // Show the loading indicator
                let loader = document.querySelector(".page-loader");
                if (!loader) {
                  loader = document.createElement("div");
                  loader.className = "page-loader";
                  document.body.appendChild(loader);
                }

                // Animation timeline
                const tl = gsap.timeline({
                  onComplete: done,
                });

                tl.to(overlay, {
                  transformOrigin: "top",
                  scaleY: 1,
                  duration: 0.6,
                  ease: "power4.inOut",
                }).to(
                  loader,
                  {
                    opacity: 1,
                    duration: 0.3,
                  },
                  "-=0.3"
                ); // Slight overlap
              },

              // Before entering new page
              async beforeEnter() {
                // Scroll to top
                window.scrollTo(0, 0);
              },

              // After entering new page
              async enter() {
                const overlay = document.querySelector(
                  ".page-transition-overlay"
                );
                const loader = document.querySelector(".page-loader");

                gsap
                  .timeline()
                  .to(loader, {
                    opacity: 0,
                    duration: 0.3,
                  })
                  .to(overlay, {
                    transformOrigin: "bottom",
                    scaleY: 0,
                    duration: 0.6,
                    ease: "power4.out",
                  });
              },
            },
          ],
        });

        // KEY CHANGE: Intercept Next.js Link click events instead of all anchor clicks
        // This is the critical part to prevent reloads
        const originalPushState = history.pushState;
        history.pushState = function (state, title, url) {
          // Run your transition animation first
          const currentUrl = window.location.pathname;

          if (currentUrl !== url && url !== null) {
            // Start the transition animation
            barba.go(url, { trigger: "barba" });

            // Delay the actual navigation slightly
            setTimeout(() => {
              router.push(url);
              originalPushState.apply(this, [state, title, url]);
            }, 800); // Adjust this timing to match your animation

            return; // Prevent immediate navigation
          }

          return originalPushState.apply(this, arguments);
        };

        // Handle regular link clicks for non-Next.js links
        document.addEventListener("click", (e) => {
          // Skip if the click was on a Next.js Link component
          if (e.target.closest('[data-next-link="true"]')) {
            return;
          }

          // Find closest anchor element
          let anchor = e.target.closest("a");
          if (!anchor) return;

          // Skip if it's an external link, has a target, or has data-no-transition
          if (
            anchor.target === "_blank" ||
            anchor.hostname !== window.location.hostname ||
            anchor.getAttribute("data-no-transition") === "true"
          ) {
            return;
          }

          // Prevent default anchor behavior
          e.preventDefault();

          // Get the href and let barba handle the transition
          const href = anchor.getAttribute("href");

          // Start Barba.js transition
          barba.go(href);

          // Then let Next.js handle the actual navigation after transition
          setTimeout(() => {
            router.push(href);
          }, 800);
        });
      } catch (error) {
        console.error("Error initializing Barba.js:", error);
      }

      return { barba, style };
    };

    // Call setupBarba function
    let cleanup = { barba: null, style: null };

    if (mounted) {
      setupBarba().then((result) => {
        if (result) cleanup = result;
      });
    }

    return () => {
      // Cleanup code
      if (typeof window !== "undefined") {
        const overlay = document.querySelector(".page-transition-overlay");
        const loader = document.querySelector(".page-loader");
        if (overlay) overlay.remove();
        if (loader) loader.remove();
        if (document.querySelector("style[data-barba-styles]")) {
          document.querySelector("style[data-barba-styles]").remove();
        }

        // Restore original history.pushState
        if (history._originalPushState) {
          history.pushState = history._originalPushState;
        }

        // Clean up Barba.js
        if (cleanup.barba && typeof cleanup.barba.destroy === "function") {
          cleanup.barba.destroy();
        }
      }
    };
  }, [mounted, router]);

  if (!mounted) {
    return <>{children}</>; // Return children without wrapper during SSR
  }

  return <div data-barba="wrapper">{children}</div>;
}
