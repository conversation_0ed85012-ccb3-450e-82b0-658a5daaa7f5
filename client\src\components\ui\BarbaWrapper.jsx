"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter, usePathname } from "next/navigation";

export default function BarbaWrapper({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  const barbaRef = useRef(null);
  const isTransitioning = useRef(false);

  useEffect(() => {
    // Set mounted to true after the component is mounted in the browser
    setMounted(true);

    // Dynamically import Barba.js and GSAP only on the client side
    const setupBarba = async () => {
      // Dynamic imports to avoid SSR issues
      const barba = (await import("@barba/core")).default;
      const gsap = (await import("gsap")).default;

      // Store barba instance for cleanup
      barbaRef.current = barba;

      // Create styles for the overlay
      const style = document.createElement("style");
      style.setAttribute("data-barba-styles", "");
      style.textContent = `
        .page-transition-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: var(--primary, #D46A6A);
          z-index: 9999;
          transform: scaleY(0);
          transform-origin: top center;
          pointer-events: none;
        }

        /* Loading indicator */
        .page-loader {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: 10000;
          width: 40px;
          height: 40px;
          pointer-events: none;
          opacity: 0;
        }

        .page-loader:after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          border: 3px solid rgba(255, 255, 255, 0.3);
          border-top-color: #fff;
          animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
      `;
      document.head.appendChild(style);

      // Custom transition function
      const performTransition = async (href) => {
        if (isTransitioning.current) return;
        isTransitioning.current = true;

        try {
          // Create the overlay if it doesn't exist
          let overlay = document.querySelector(".page-transition-overlay");
          if (!overlay) {
            overlay = document.createElement("div");
            overlay.className = "page-transition-overlay";
            document.body.appendChild(overlay);
          }

          // Show the loading indicator
          let loader = document.querySelector(".page-loader");
          if (!loader) {
            loader = document.createElement("div");
            loader.className = "page-loader";
            document.body.appendChild(loader);
          }

          // Leave animation
          await new Promise((resolve) => {
            const tl = gsap.timeline({
              onComplete: resolve,
            });

            tl.to(overlay, {
              transformOrigin: "top",
              scaleY: 1,
              duration: 0.6,
              ease: "power4.inOut",
            }).to(
              loader,
              {
                opacity: 1,
                duration: 0.3,
              },
              "-=0.3"
            );
          });

          // Navigate to new page
          router.push(href);

          // Wait a bit for the navigation to complete
          await new Promise(resolve => setTimeout(resolve, 100));

          // Scroll to top
          window.scrollTo(0, 0);

          // Enter animation
          await new Promise((resolve) => {
            const tl = gsap.timeline({
              onComplete: resolve,
            });

            tl.to(loader, {
              opacity: 0,
              duration: 0.3,
            }).to(overlay, {
              transformOrigin: "bottom",
              scaleY: 0,
              duration: 0.6,
              ease: "power4.out",
            });
          });

        } catch (error) {
          console.error("Transition error:", error);
        } finally {
          isTransitioning.current = false;
        }
      };

      try {
        // Initialize barba with minimal configuration
        barba.init({
          // Disable automatic link handling
          prevent: () => true,
          // Disable transitions - we'll handle them manually
          transitions: [],
        });

        // Handle custom barba click events
        const handleBarbaClick = (e) => {
          const href = e.detail.href;
          if (href && href !== pathname) {
            performTransition(href);
          }
        };

        // Handle regular link clicks
        const handleLinkClick = (e) => {
          // Skip if currently transitioning
          if (isTransitioning.current) {
            e.preventDefault();
            return;
          }

          // Find closest anchor element
          let anchor = e.target.closest("a");
          if (!anchor) return;

          // Skip if it's marked as a barba link (handled by BarbaLink component)
          if (anchor.getAttribute("data-barba-link") === "true") {
            return;
          }

          // Skip if it's an external link, has a target, or has data-no-transition
          if (
            anchor.target === "_blank" ||
            anchor.hostname !== window.location.hostname ||
            anchor.getAttribute("data-no-transition") === "true" ||
            anchor.getAttribute("href")?.startsWith("#") ||
            anchor.getAttribute("href")?.startsWith("mailto:") ||
            anchor.getAttribute("href")?.startsWith("tel:")
          ) {
            return;
          }

          // Get the href
          const href = anchor.getAttribute("href");
          if (!href || href === pathname) return;

          // Prevent default and perform transition
          e.preventDefault();
          performTransition(href);
        };

        // Add event listeners
        document.addEventListener("barba:click", handleBarbaClick);
        document.addEventListener("click", handleLinkClick);

        // Store cleanup functions
        return {
          barba,
          style,
          cleanup: () => {
            document.removeEventListener("barba:click", handleBarbaClick);
            document.removeEventListener("click", handleLinkClick);
          }
        };
      } catch (error) {
        console.error("Error initializing Barba.js:", error);
        return null;
      }
    };

    // Call setupBarba function
    let cleanup = null;

    if (mounted) {
      setupBarba().then((result) => {
        cleanup = result;
      });
    }

    return () => {
      // Cleanup code
      if (typeof window !== "undefined") {
        const overlay = document.querySelector(".page-transition-overlay");
        const loader = document.querySelector(".page-loader");
        if (overlay) overlay.remove();
        if (loader) loader.remove();
        if (document.querySelector("style[data-barba-styles]")) {
          document.querySelector("style[data-barba-styles]").remove();
        }

        // Clean up event listeners and Barba.js
        if (cleanup) {
          if (cleanup.cleanup) {
            cleanup.cleanup();
          }
          if (cleanup.barba && typeof cleanup.barba.destroy === "function") {
            cleanup.barba.destroy();
          }
        }

        // Reset transition state
        isTransitioning.current = false;
      }
    };
  }, [mounted, router, pathname]);

  if (!mounted) {
    return <>{children}</>; // Return children without wrapper during SSR
  }

  return <div data-barba="wrapper">{children}</div>;
}
