"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"34d84a5e46eb\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERlc2t0b3BcXFBSRUVUSVpFTlxcY2xpZW50XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzNGQ4NGE1ZTQ2ZWJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/PreloaderWrapper.jsx":
/*!************************************************!*\
  !*** ./src/components/ui/PreloaderWrapper.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PreloaderWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Preloader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Preloader */ \"(app-pages-browser)/./src/components/ui/Preloader.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PreloaderWrapper(param) {\n    let { children } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showContent, setShowContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PreloaderWrapper.useEffect\": ()=>{\n            // Simulate initial page load time - increased for better experience\n            const minLoadTime = 2500; // Minimum 2.5 seconds for better feel\n            const startTime = Date.now();\n            // Check if page is already loaded\n            const checkPageLoad = {\n                \"PreloaderWrapper.useEffect.checkPageLoad\": ()=>{\n                    const elapsedTime = Date.now() - startTime;\n                    const remainingTime = Math.max(0, minLoadTime - elapsedTime);\n                    setTimeout({\n                        \"PreloaderWrapper.useEffect.checkPageLoad\": ()=>{\n                            setIsLoading(false);\n                            // Small delay before showing content for smooth transition\n                            setTimeout({\n                                \"PreloaderWrapper.useEffect.checkPageLoad\": ()=>{\n                                    setShowContent(true);\n                                }\n                            }[\"PreloaderWrapper.useEffect.checkPageLoad\"], 300);\n                        }\n                    }[\"PreloaderWrapper.useEffect.checkPageLoad\"], remainingTime);\n                }\n            }[\"PreloaderWrapper.useEffect.checkPageLoad\"];\n            // Wait for DOM to be ready\n            if (document.readyState === 'complete') {\n                checkPageLoad();\n            } else {\n                window.addEventListener('load', checkPageLoad);\n                return ({\n                    \"PreloaderWrapper.useEffect\": ()=>window.removeEventListener('load', checkPageLoad)\n                })[\"PreloaderWrapper.useEffect\"];\n            }\n        }\n    }[\"PreloaderWrapper.useEffect\"], []);\n    const handlePreloaderComplete = ()=>{\n        setShowContent(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Preloader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onComplete: handlePreloaderComplete\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\PreloaderWrapper.jsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"transition-opacity duration-500 \".concat(showContent ? 'opacity-100' : 'opacity-0'),\n                style: {\n                    visibility: showContent ? 'visible' : 'hidden',\n                    position: showContent ? 'relative' : 'absolute',\n                    top: showContent ? 'auto' : '-9999px'\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\PreloaderWrapper.jsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(PreloaderWrapper, \"Wg9CMEj217jKwwObb8yLWReRpck=\");\n_c = PreloaderWrapper;\nvar _c;\n$RefreshReg$(_c, \"PreloaderWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/PreloaderWrapper.jsx\n"));

/***/ })

});