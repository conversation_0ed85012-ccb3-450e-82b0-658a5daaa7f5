"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/policies/returns/page",{

/***/ "(app-pages-browser)/./src/app/policies/returns/page.js":
/*!******************************************!*\
  !*** ./src/app/policies/returns/page.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReturnsPolicy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_policies_common_PolicyHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/policies/common/PolicyHeader */ \"(app-pages-browser)/./src/components/policies/common/PolicyHeader.jsx\");\n/* harmony import */ var _components_policies_returns_ReturnSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/policies/returns/ReturnSection */ \"(app-pages-browser)/./src/components/policies/returns/ReturnSection.jsx\");\n/* harmony import */ var _components_policies_returns_ReturnProcess__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/policies/returns/ReturnProcess */ \"(app-pages-browser)/./src/components/policies/returns/ReturnProcess.jsx\");\n/* harmony import */ var _components_policies_returns_EligibilityCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/policies/returns/EligibilityCard */ \"(app-pages-browser)/./src/components/policies/returns/EligibilityCard.jsx\");\n/* harmony import */ var _components_policies_returns_PolicyTimeline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/policies/returns/PolicyTimeline */ \"(app-pages-browser)/./src/components/policies/returns/PolicyTimeline.jsx\");\n/* harmony import */ var _components_policies_returns_ContactBanner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/policies/returns/ContactBanner */ \"(app-pages-browser)/./src/components/policies/returns/ContactBanner.jsx\");\n/* harmony import */ var _data_returns_policy__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/data/returns-policy */ \"(app-pages-browser)/./src/data/returns-policy.js\");\n/* harmony import */ var _components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/PageTransition */ \"(app-pages-browser)/./src/components/ui/PageTransition.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\nfunction ReturnsPolicy() {\n    const container = {\n        hidden: {\n            opacity: 0\n        },\n        show: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.15,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const item = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        show: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.7\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_common_PolicyHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Returns & Exchange Policy\",\n                    description: \"Our commitment to your satisfaction and shopping confidence\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#FFFBF7]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.main, {\n                        className: \"container mx-auto py-12 px-4 md:px-6\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            className: \"max-w-4xl mx-auto\",\n                            variants: container,\n                            initial: \"hidden\",\n                            animate: \"show\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: item,\n                                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-10 pb-6 border-b border-slate-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 md:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-500\",\n                                                    children: \"Last Updated\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-[var(--heading)]\",\n                                                    children: \"March 5, 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 bg-[var(--foreground)] rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-[var(--accent)]\",\n                                                children: \"Official Policy Document\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                lineNumber: 66,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                            lineNumber: 65,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: item,\n                                    className: \"mb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ReturnSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Our Return Philosophy\",\n                                        icon: \"philosophy\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"At Preetizen, we craft our pieces with care in limited batches, encouraging thoughtful purchases. While we strive for perfect products and service, we understand that exceptions occur. This policy outlines our procedures for exchanges, returns, and refunds.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                            lineNumber: 75,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: item,\n                                    className: \"mb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ReturnSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Exchange Eligibility\",\n                                        icon: \"eligibility\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mt-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_EligibilityCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    title: \"What We Accept\",\n                                                    items: _data_returns_policy__WEBPACK_IMPORTED_MODULE_8__.eligibilityItems,\n                                                    type: \"eligible\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_EligibilityCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    title: \"What We Cannot Accept\",\n                                                    items: _data_returns_policy__WEBPACK_IMPORTED_MODULE_8__.nonEligibleItems,\n                                                    type: \"ineligible\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                        lineNumber: 87,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: item,\n                                    className: \"mb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ReturnSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Return Process\",\n                                        icon: \"process\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ReturnProcess__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                steps: _data_returns_policy__WEBPACK_IMPORTED_MODULE_8__.processSteps\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                lineNumber: 107,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                            lineNumber: 106,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: item,\n                                    className: \"mb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ReturnSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Refund Options\",\n                                        icon: \"refund\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white p-6 rounded-xl shadow-sm border border-slate-100 hover:shadow-md transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-[var(--primary)]/20 bg-opacity-10 rounded-full flex items-center justify-center mr-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-[var(--primary)]\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                                lineNumber: 125,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                            lineNumber: 119,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                        lineNumber: 118,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-lg\",\n                                                                        children: \"Original Payment Method\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 117,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-600\",\n                                                                children: \"Full refund to your original payment method, processed within 7–10 business days after our team inspects the returned item.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white p-6 rounded-xl shadow-sm border border-slate-100 hover:shadow-md transition-shadow\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-10 h-10 bg-[var(--accent)]/20 bg-opacity-10 rounded-full flex items-center justify-center mr-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-5 h-5 text-[var(--accent)]\",\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                                lineNumber: 153,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                            lineNumber: 147,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                        lineNumber: 146,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-lg\",\n                                                                        children: \"Store Credit\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-slate-600\",\n                                                                children: \"Opt for store credit that can be used on future purchases. Store credit is issued promptly after inspection and never expires.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-8 bg-slate-50 p-6 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-slate-800 mb-3\",\n                                                        children: \"Additional Refund Information:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside text-slate-600 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Prepaid payments will be refunded to the original payment method\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Cash-on-delivery (COD) refunds require secure sharing of bank details\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Refunds do not include original shipping charges\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: item,\n                                    className: \"mb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ReturnSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Return Shipping\",\n                                        icon: \"shipping\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 bg-white p-6 rounded-xl shadow-sm border border-slate-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-600 mb-4\",\n                                                    children: \"We currently don't offer a pickup service for returns. Customers are responsible for shipping items back to us at:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-50 p-4 rounded-lg mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-slate-700\",\n                                                            children: \"Preetizen Returns Department\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"123 Fashion Street, Design District\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: \"Mumbai, Maharashtra 400001\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-[var(--highlight)]/20 bg-opacity-10 rounded-full flex-shrink-0 flex items-center justify-center mr-4 mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-[var(--highlight)]\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-slate-800\",\n                                                                    children: \"Shipping Reimbursement:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                \"Preetizen will reimburse up to ₹50 of your return shipping cost once you provide a valid shipping receipt. This will be added to your refund amount or store credit.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                            lineNumber: 193,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    variants: item,\n                                    className: \"mb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ReturnSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        title: \"Policy Timeline\",\n                                        icon: \"timeline\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_PolicyTimeline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                                lineNumber: 244,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                            lineNumber: 243,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                        lineNumber: 242,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_returns_ContactBanner__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    email: \"<EMAIL>\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\returns\\\\page.js\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c = ReturnsPolicy;\nvar _c;\n$RefreshReg$(_c, \"ReturnsPolicy\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/policies/returns/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/PageTransition.jsx":
/*!**********************************************!*\
  !*** ./src/components/ui/PageTransition.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst PageTransition = (param)=>{\n    let { children } = param;\n    // Enhanced component that properly marks content for Barba\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"page-content\",\n        \"data-barba\": \"container\",\n        \"data-barba-namespace\": \"default\",\n        suppressHydrationWarning: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\PageTransition.jsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PageTransition;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageTransition);\nvar _c;\n$RefreshReg$(_c, \"PageTransition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1BhZ2VUcmFuc2l0aW9uLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUMwQjtBQUUxQixNQUFNQyxpQkFBaUI7UUFBQyxFQUFFQyxRQUFRLEVBQUU7SUFDbEMsMkRBQTJEO0lBQzNELHFCQUNFLDhEQUFDQztRQUNDQyxXQUFVO1FBQ1ZDLGNBQVc7UUFDWEMsd0JBQXFCO1FBQ3JCQyx3QkFBd0I7a0JBRXZCTDs7Ozs7O0FBR1A7S0FaTUQ7QUFjTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXNrdG9wXFxQUkVFVElaRU5cXGNsaWVudFxcc3JjXFxjb21wb25lbnRzXFx1aVxcUGFnZVRyYW5zaXRpb24uanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5jb25zdCBQYWdlVHJhbnNpdGlvbiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICAvLyBFbmhhbmNlZCBjb21wb25lbnQgdGhhdCBwcm9wZXJseSBtYXJrcyBjb250ZW50IGZvciBCYXJiYVxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IFxyXG4gICAgICBjbGFzc05hbWU9XCJwYWdlLWNvbnRlbnRcIiBcclxuICAgICAgZGF0YS1iYXJiYT1cImNvbnRhaW5lclwiIFxyXG4gICAgICBkYXRhLWJhcmJhLW5hbWVzcGFjZT1cImRlZmF1bHRcIlxyXG4gICAgICBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmcgLy8gSW1wb3J0YW50IGZvciBOZXh0LmpzXHJcbiAgICA+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQYWdlVHJhbnNpdGlvbjsiXSwibmFtZXMiOlsiUmVhY3QiLCJQYWdlVHJhbnNpdGlvbiIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YS1iYXJiYSIsImRhdGEtYmFyYmEtbmFtZXNwYWNlIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/PageTransition.jsx\n"));

/***/ })

});