"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/policies/shipping/page",{

/***/ "(app-pages-browser)/./src/app/policies/shipping/page.js":
/*!*******************************************!*\
  !*** ./src/app/policies/shipping/page.js ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShippingPolicyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_common_PageSEO__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/common/PageSEO */ \"(app-pages-browser)/./src/components/common/PageSEO.jsx\");\n/* harmony import */ var _components_policies_shipping_ShippingPolicy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/policies/shipping/ShippingPolicy */ \"(app-pages-browser)/./src/components/policies/shipping/ShippingPolicy.jsx\");\n/* harmony import */ var _components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/PageTransition */ \"(app-pages-browser)/./src/components/ui/PageTransition.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ShippingPolicyPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_PageSEO__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Shipping & Delivery Policy | Preetizen\",\n                    description: \"Learn about Preetizen's shipping and delivery process, timeframes, courier partners, and more. We deliver sustainable fashion across India.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\shipping\\\\page.js\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_shipping_ShippingPolicy__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\shipping\\\\page.js\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\shipping\\\\page.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = ShippingPolicyPage;\nvar _c;\n$RefreshReg$(_c, \"ShippingPolicyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcG9saWNpZXMvc2hpcHBpbmcvcGFnZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUMwQjtBQUN3QjtBQUN5QjtBQUNmO0FBRTdDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDRCxxRUFBY0E7a0JBQ2I7OzhCQUNFLDhEQUFDRixrRUFBT0E7b0JBQ05JLE9BQU07b0JBQ05DLGFBQVk7Ozs7Ozs4QkFFZCw4REFBQ0osb0ZBQWNBOzs7Ozs7Ozs7Ozs7QUFJdkI7S0Fad0JFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERlc2t0b3BcXFBSRUVUSVpFTlxcY2xpZW50XFxzcmNcXGFwcFxccG9saWNpZXNcXHNoaXBwaW5nXFxwYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCBQYWdlU0VPIGZyb20gXCJAL2NvbXBvbmVudHMvY29tbW9uL1BhZ2VTRU9cIjtcclxuaW1wb3J0IFNoaXBwaW5nUG9saWN5IGZyb20gXCJAL2NvbXBvbmVudHMvcG9saWNpZXMvc2hpcHBpbmcvU2hpcHBpbmdQb2xpY3lcIjtcclxuaW1wb3J0IFBhZ2VUcmFuc2l0aW9uIGZyb20gXCJAL2NvbXBvbmVudHMvdWkvUGFnZVRyYW5zaXRpb25cIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNoaXBwaW5nUG9saWN5UGFnZSgpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPFBhZ2VUcmFuc2l0aW9uPlxyXG4gICAgICA8PlxyXG4gICAgICAgIDxQYWdlU0VPXHJcbiAgICAgICAgICB0aXRsZT1cIlNoaXBwaW5nICYgRGVsaXZlcnkgUG9saWN5IHwgUHJlZXRpemVuXCJcclxuICAgICAgICAgIGRlc2NyaXB0aW9uPVwiTGVhcm4gYWJvdXQgUHJlZXRpemVuJ3Mgc2hpcHBpbmcgYW5kIGRlbGl2ZXJ5IHByb2Nlc3MsIHRpbWVmcmFtZXMsIGNvdXJpZXIgcGFydG5lcnMsIGFuZCBtb3JlLiBXZSBkZWxpdmVyIHN1c3RhaW5hYmxlIGZhc2hpb24gYWNyb3NzIEluZGlhLlwiXHJcbiAgICAgICAgLz5cclxuICAgICAgICA8U2hpcHBpbmdQb2xpY3kgLz5cclxuICAgICAgPC8+XHJcbiAgICA8L1BhZ2VUcmFuc2l0aW9uPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiUGFnZVNFTyIsIlNoaXBwaW5nUG9saWN5IiwiUGFnZVRyYW5zaXRpb24iLCJTaGlwcGluZ1BvbGljeVBhZ2UiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/policies/shipping/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/PageTransition.jsx":
/*!**********************************************!*\
  !*** ./src/components/ui/PageTransition.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst PageTransition = (param)=>{\n    let { children } = param;\n    // Enhanced component that properly marks content for Barba\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"page-content\",\n        \"data-barba\": \"container\",\n        \"data-barba-namespace\": \"default\",\n        suppressHydrationWarning: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\PageTransition.jsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PageTransition;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageTransition);\nvar _c;\n$RefreshReg$(_c, \"PageTransition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1BhZ2VUcmFuc2l0aW9uLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUMwQjtBQUUxQixNQUFNQyxpQkFBaUI7UUFBQyxFQUFFQyxRQUFRLEVBQUU7SUFDbEMsMkRBQTJEO0lBQzNELHFCQUNFLDhEQUFDQztRQUNDQyxXQUFVO1FBQ1ZDLGNBQVc7UUFDWEMsd0JBQXFCO1FBQ3JCQyx3QkFBd0I7a0JBRXZCTDs7Ozs7O0FBR1A7S0FaTUQ7QUFjTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXNrdG9wXFxQUkVFVElaRU5cXGNsaWVudFxcc3JjXFxjb21wb25lbnRzXFx1aVxcUGFnZVRyYW5zaXRpb24uanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5jb25zdCBQYWdlVHJhbnNpdGlvbiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICAvLyBFbmhhbmNlZCBjb21wb25lbnQgdGhhdCBwcm9wZXJseSBtYXJrcyBjb250ZW50IGZvciBCYXJiYVxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IFxyXG4gICAgICBjbGFzc05hbWU9XCJwYWdlLWNvbnRlbnRcIiBcclxuICAgICAgZGF0YS1iYXJiYT1cImNvbnRhaW5lclwiIFxyXG4gICAgICBkYXRhLWJhcmJhLW5hbWVzcGFjZT1cImRlZmF1bHRcIlxyXG4gICAgICBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmcgLy8gSW1wb3J0YW50IGZvciBOZXh0LmpzXHJcbiAgICA+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQYWdlVHJhbnNpdGlvbjsiXSwibmFtZXMiOlsiUmVhY3QiLCJQYWdlVHJhbnNpdGlvbiIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YS1iYXJiYSIsImRhdGEtYmFyYmEtbmFtZXNwYWNlIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/PageTransition.jsx\n"));

/***/ })

});