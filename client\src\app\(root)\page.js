"use client";

import React from "react";
import { motion } from "framer-motion";
import Hero from "@/components/home/<USER>";
import BrandStory from "@/components/home/<USER>";
import FeaturedCollection from "@/components/home/<USER>";
import CommunitySpotlight from "@/components/home/<USER>";
import Newsletter from "@/components/home/<USER>";
import StudentDiscount from "@/components/home/<USER>";
import PageTransition from "@/components/ui/PageTransition";

export default function Home() {
  return (
    <PageTransition>
      <main className="min-h-screen bg-[#FDF8F2]">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Hero />
          <BrandStory />
          <FeaturedCollection />
          <CommunitySpotlight />
          <StudentDiscount />
          <Newsletter />
        </motion.div>
      </main>
    </PageTransition>
  );
}
