"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f48cdf3b8abd\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERlc2t0b3BcXFBSRUVUSVpFTlxcY2xpZW50XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmNDhjZGYzYjhhYmRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/AnnouncementBanner.jsx":
/*!**************************************************!*\
  !*** ./src/components/ui/AnnouncementBanner.jsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst AnnouncementBanner = (param)=>{\n    let { isVisible } = param;\n    const bannerText = \"NEW COLLECTION DROP INCOMING!\";\n    const repeatedText = Array(10).fill(bannerText).join(' • ');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"bg-[#D46A6A] overflow-hidden\",\n        initial: {\n            height: 0,\n            opacity: 0\n        },\n        animate: {\n            height: isVisible ? 'auto' : 0,\n            opacity: isVisible ? 1 : 0\n        },\n        transition: {\n            duration: 0.4,\n            ease: 'easeInOut'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center py-2 whitespace-nowrap\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"flex items-center text-white text-sm font-medium\",\n                animate: {\n                    x: [\n                        '0%',\n                        '-50%'\n                    ]\n                },\n                transition: {\n                    x: {\n                        repeat: Infinity,\n                        repeatType: 'loop',\n                        duration: 30,\n                        ease: 'linear'\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-block px-4 font-[BoskaItalic\",\n                        children: repeatedText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\AnnouncementBanner.jsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-block px-4 \",\n                        children: repeatedText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\AnnouncementBanner.jsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\AnnouncementBanner.jsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\AnnouncementBanner.jsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\AnnouncementBanner.jsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AnnouncementBanner;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnnouncementBanner);\nvar _c;\n$RefreshReg$(_c, \"AnnouncementBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/AnnouncementBanner.jsx\n"));

/***/ })

});