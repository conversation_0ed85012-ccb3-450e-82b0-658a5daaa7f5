"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1ab8e8ba7989\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERlc2t0b3BcXFBSRUVUSVpFTlxcY2xpZW50XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxYWI4ZThiYTc5ODlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/BarbaWrapper.jsx":
/*!********************************************!*\
  !*** ./src/components/ui/BarbaWrapper.jsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BarbaWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction BarbaWrapper(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const barbaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isTransitioning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BarbaWrapper.useEffect\": ()=>{\n            // Set mounted to true after the component is mounted in the browser\n            setMounted(true);\n            // Dynamically import Barba.js and GSAP only on the client side\n            const setupBarba = {\n                \"BarbaWrapper.useEffect.setupBarba\": async ()=>{\n                    // Dynamic imports to avoid SSR issues\n                    const barba = (await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_barba_core_dist_barba_umd_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! @barba/core */ \"(app-pages-browser)/./node_modules/@barba/core/dist/barba.umd.js\", 23))).default;\n                    const gsap = (await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_gsap_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\"))).default;\n                    // Store barba instance for cleanup\n                    barbaRef.current = barba;\n                    // Create styles for the overlay\n                    const style = document.createElement(\"style\");\n                    style.setAttribute(\"data-barba-styles\", \"\");\n                    style.textContent = '\\n        .page-transition-overlay {\\n          position: fixed;\\n          top: 0;\\n          left: 0;\\n          right: 0;\\n          bottom: 0;\\n          background-color: var(--primary, #D46A6A);\\n          z-index: 9999;\\n          transform: scaleY(0);\\n          transform-origin: top center;\\n          pointer-events: none;\\n        }\\n\\n        /* Loading indicator */\\n        .page-loader {\\n          position: fixed;\\n          top: 50%;\\n          left: 50%;\\n          transform: translate(-50%, -50%);\\n          z-index: 10000;\\n          width: 40px;\\n          height: 40px;\\n          pointer-events: none;\\n          opacity: 0;\\n        }\\n\\n        .page-loader:after {\\n          content: \"\";\\n          position: absolute;\\n          top: 0;\\n          left: 0;\\n          width: 100%;\\n          height: 100%;\\n          border-radius: 50%;\\n          border: 3px solid rgba(255, 255, 255, 0.3);\\n          border-top-color: #fff;\\n          animation: spin 1s ease-in-out infinite;\\n        }\\n\\n        @keyframes spin {\\n          to {\\n            transform: rotate(360deg);\\n          }\\n        }\\n      ';\n                    document.head.appendChild(style);\n                    // Custom transition function\n                    const performTransition = {\n                        \"BarbaWrapper.useEffect.setupBarba.performTransition\": async (href)=>{\n                            if (isTransitioning.current) return;\n                            isTransitioning.current = true;\n                            try {\n                                // Create the overlay if it doesn't exist\n                                let overlay = document.querySelector(\".page-transition-overlay\");\n                                if (!overlay) {\n                                    overlay = document.createElement(\"div\");\n                                    overlay.className = \"page-transition-overlay\";\n                                    document.body.appendChild(overlay);\n                                }\n                                // Show the loading indicator\n                                let loader = document.querySelector(\".page-loader\");\n                                if (!loader) {\n                                    loader = document.createElement(\"div\");\n                                    loader.className = \"page-loader\";\n                                    document.body.appendChild(loader);\n                                }\n                                // Leave animation\n                                await new Promise({\n                                    \"BarbaWrapper.useEffect.setupBarba.performTransition\": (resolve)=>{\n                                        const tl = gsap.timeline({\n                                            onComplete: resolve\n                                        });\n                                        tl.to(overlay, {\n                                            transformOrigin: \"top\",\n                                            scaleY: 1,\n                                            duration: 0.6,\n                                            ease: \"power4.inOut\"\n                                        }).to(loader, {\n                                            opacity: 1,\n                                            duration: 0.3\n                                        }, \"-=0.3\");\n                                    }\n                                }[\"BarbaWrapper.useEffect.setupBarba.performTransition\"]);\n                                // Navigate to new page\n                                router.push(href);\n                                // Wait a bit for the navigation to complete\n                                await new Promise({\n                                    \"BarbaWrapper.useEffect.setupBarba.performTransition\": (resolve)=>setTimeout(resolve, 100)\n                                }[\"BarbaWrapper.useEffect.setupBarba.performTransition\"]);\n                                // Scroll to top\n                                window.scrollTo(0, 0);\n                                // Enter animation\n                                await new Promise({\n                                    \"BarbaWrapper.useEffect.setupBarba.performTransition\": (resolve)=>{\n                                        const tl = gsap.timeline({\n                                            onComplete: resolve\n                                        });\n                                        tl.to(loader, {\n                                            opacity: 0,\n                                            duration: 0.3\n                                        }).to(overlay, {\n                                            transformOrigin: \"bottom\",\n                                            scaleY: 0,\n                                            duration: 0.6,\n                                            ease: \"power4.out\"\n                                        });\n                                    }\n                                }[\"BarbaWrapper.useEffect.setupBarba.performTransition\"]);\n                            } catch (error) {\n                                console.error(\"Transition error:\", error);\n                            } finally{\n                                isTransitioning.current = false;\n                            }\n                        }\n                    }[\"BarbaWrapper.useEffect.setupBarba.performTransition\"];\n                    try {\n                        // Initialize barba with minimal configuration\n                        barba.init({\n                            // Disable automatic link handling\n                            prevent: {\n                                \"BarbaWrapper.useEffect.setupBarba\": ()=>true\n                            }[\"BarbaWrapper.useEffect.setupBarba\"],\n                            // Disable transitions - we'll handle them manually\n                            transitions: []\n                        });\n                        // KEY CHANGE: Intercept Next.js Link click events instead of all anchor clicks\n                        // This is the critical part to prevent reloads\n                        const originalPushState = history.pushState;\n                        history.pushState = ({\n                            \"BarbaWrapper.useEffect.setupBarba\": function(state, title, url) {\n                                // Run your transition animation first\n                                const currentUrl = window.location.pathname;\n                                if (currentUrl !== url && url !== null) {\n                                    // Start the transition animation\n                                    barba.go(url, {\n                                        trigger: \"barba\"\n                                    });\n                                    // Delay the actual navigation slightly\n                                    setTimeout({\n                                        \"BarbaWrapper.useEffect.setupBarba\": ()=>{\n                                            router.push(url);\n                                            originalPushState.apply(this, [\n                                                state,\n                                                title,\n                                                url\n                                            ]);\n                                        }\n                                    }[\"BarbaWrapper.useEffect.setupBarba\"], 800); // Adjust this timing to match your animation\n                                    return; // Prevent immediate navigation\n                                }\n                                return originalPushState.apply(this, arguments);\n                            }\n                        })[\"BarbaWrapper.useEffect.setupBarba\"];\n                        // Handle regular link clicks for non-Next.js links\n                        document.addEventListener(\"click\", {\n                            \"BarbaWrapper.useEffect.setupBarba\": (e)=>{\n                                // Skip if the click was on a Next.js Link component\n                                if (e.target.closest('[data-next-link=\"true\"]')) {\n                                    return;\n                                }\n                                // Find closest anchor element\n                                let anchor = e.target.closest(\"a\");\n                                if (!anchor) return;\n                                // Skip if it's an external link, has a target, or has data-no-transition\n                                if (anchor.target === \"_blank\" || anchor.hostname !== window.location.hostname || anchor.getAttribute(\"data-no-transition\") === \"true\") {\n                                    return;\n                                }\n                                // Prevent default anchor behavior\n                                e.preventDefault();\n                                // Get the href and let barba handle the transition\n                                const href = anchor.getAttribute(\"href\");\n                                // Start Barba.js transition\n                                barba.go(href);\n                                // Then let Next.js handle the actual navigation after transition\n                                setTimeout({\n                                    \"BarbaWrapper.useEffect.setupBarba\": ()=>{\n                                        router.push(href);\n                                    }\n                                }[\"BarbaWrapper.useEffect.setupBarba\"], 800);\n                            }\n                        }[\"BarbaWrapper.useEffect.setupBarba\"]);\n                    } catch (error) {\n                        console.error(\"Error initializing Barba.js:\", error);\n                    }\n                    return {\n                        barba,\n                        style\n                    };\n                }\n            }[\"BarbaWrapper.useEffect.setupBarba\"];\n            // Call setupBarba function\n            let cleanup = {\n                barba: null,\n                style: null\n            };\n            if (mounted) {\n                setupBarba().then({\n                    \"BarbaWrapper.useEffect\": (result)=>{\n                        if (result) cleanup = result;\n                    }\n                }[\"BarbaWrapper.useEffect\"]);\n            }\n            return ({\n                \"BarbaWrapper.useEffect\": ()=>{\n                    // Cleanup code\n                    if (true) {\n                        const overlay = document.querySelector(\".page-transition-overlay\");\n                        const loader = document.querySelector(\".page-loader\");\n                        if (overlay) overlay.remove();\n                        if (loader) loader.remove();\n                        if (document.querySelector(\"style[data-barba-styles]\")) {\n                            document.querySelector(\"style[data-barba-styles]\").remove();\n                        }\n                        // Restore original history.pushState\n                        if (history._originalPushState) {\n                            history.pushState = history._originalPushState;\n                        }\n                        // Clean up Barba.js\n                        if (cleanup.barba && typeof cleanup.barba.destroy === \"function\") {\n                            cleanup.barba.destroy();\n                        }\n                    }\n                }\n            })[\"BarbaWrapper.useEffect\"];\n        }\n    }[\"BarbaWrapper.useEffect\"], [\n        mounted,\n        router\n    ]);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false); // Return children without wrapper during SSR\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-barba\": \"wrapper\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\BarbaWrapper.jsx\",\n        lineNumber: 263,\n        columnNumber: 10\n    }, this);\n}\n_s(BarbaWrapper, \"WljeqVfokeR3mAnXecgiKWsvBZE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = BarbaWrapper;\nvar _c;\n$RefreshReg$(_c, \"BarbaWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/BarbaWrapper.jsx\n"));

/***/ })

});