"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reviews/page",{

/***/ "(app-pages-browser)/./src/app/reviews/page.js":
/*!*********************************!*\
  !*** ./src/app/reviews/page.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReviewsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_PageHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/PageHeader */ \"(app-pages-browser)/./src/components/ui/PageHeader.jsx\");\n/* harmony import */ var _components_reviews_ReviewStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/reviews/ReviewStats */ \"(app-pages-browser)/./src/components/reviews/ReviewStats.jsx\");\n/* harmony import */ var _components_reviews_ReviewGallery__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/reviews/ReviewGallery */ \"(app-pages-browser)/./src/components/reviews/ReviewGallery.jsx\");\n/* harmony import */ var _components_reviews_FeaturedReviews__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/reviews/FeaturedReviews */ \"(app-pages-browser)/./src/components/reviews/FeaturedReviews.jsx\");\n/* harmony import */ var _components_reviews_TestimonialCarousel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/reviews/TestimonialCarousel */ \"(app-pages-browser)/./src/components/reviews/TestimonialCarousel.jsx\");\n/* harmony import */ var _components_reviews_ReviewForm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/reviews/ReviewForm */ \"(app-pages-browser)/./src/components/reviews/ReviewForm.jsx\");\n/* harmony import */ var _components_reviews_FilterableReviewList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/reviews/FilterableReviewList */ \"(app-pages-browser)/./src/components/reviews/FilterableReviewList.jsx\");\n/* harmony import */ var _components_reviews_ReviewSchema__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/reviews/ReviewSchema */ \"(app-pages-browser)/./src/components/reviews/ReviewSchema.jsx\");\n/* harmony import */ var _components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/PageTransition */ \"(app-pages-browser)/./src/components/ui/PageTransition.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction ReviewsPage() {\n    _s();\n    const formRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isFormInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_11__.useInView)(formRef, {\n        once: true,\n        amount: 0.3\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ReviewsPage.useEffect\": ()=>{\n            // Scroll to form if URL has #write-review\n            if (window.location.hash === \"#write-review\") {\n                var _formRef_current;\n                (_formRef_current = formRef.current) === null || _formRef_current === void 0 ? void 0 : _formRef_current.scrollIntoView({\n                    behavior: \"smooth\"\n                });\n            }\n        }\n    }[\"ReviewsPage.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-[#FFFBF7]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reviews_ReviewSchema__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Customer Reviews\",\n                    subtitle: \"Read what our community has to say about their Preetizen experience\",\n                    imageSrc: \"/images/reviews-header.jpg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-12 bg-[#F8F3E9]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-2xl md:text-3xl font-serif text-center text-[#6B4F3B] mb-8\",\n                                children: \"Our Community in Preetizen\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reviews_ReviewGallery__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-16 md:py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-2xl md:text-3xl font-serif text-center text-[#6B4F3B] mb-2\",\n                                children: \"Featured Stories\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-center text-[#6B4F3B]/70 mb-12 max-w-2xl mx-auto\",\n                                children: \"Real stories from our customers about their experience with our sustainable fashion\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reviews_FeaturedReviews__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-16 bg-[#A7BFA3]/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-2xl md:text-3xl font-serif text-center text-[#6B4F3B] mb-12\",\n                                children: \"What Our Customers Say\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reviews_TestimonialCarousel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-16 md:py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-2xl md:text-3xl font-serif text-center text-[#6B4F3B] mb-2\",\n                                children: \"All Reviews\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"text-center text-[#6B4F3B]/70 mb-12 max-w-2xl mx-auto\",\n                                children: \"Filter and sort through all customer reviews to find what interests you\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reviews_FilterableReviewList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    ref: formRef,\n                    className: \"py-16 md:py-20 bg-[#F8F3E9]\",\n                    id: \"write-review\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: isFormInView ? {\n                                    opacity: 1,\n                                    y: 0\n                                } : {},\n                                transition: {\n                                    duration: 0.5\n                                },\n                                className: \"text-2xl md:text-3xl font-serif text-center text-[#6B4F3B] mb-2\",\n                                children: \"Share Your Experience\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: isFormInView ? {\n                                    opacity: 1,\n                                    y: 0\n                                } : {},\n                                transition: {\n                                    duration: 0.5,\n                                    delay: 0.1\n                                },\n                                className: \"text-center text-[#6B4F3B]/70 mb-12 max-w-2xl mx-auto\",\n                                children: \"Loved your Preetizen purchase? Let us and others know about your experience!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_reviews_ReviewForm__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\reviews\\\\page.js\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n_s(ReviewsPage, \"afa7emLAcEQZhqkq6g9bjpwetl0=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_11__.useInView\n    ];\n});\n_c = ReviewsPage;\nvar _c;\n$RefreshReg$(_c, \"ReviewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/reviews/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/PageTransition.jsx":
/*!**********************************************!*\
  !*** ./src/components/ui/PageTransition.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst PageTransition = (param)=>{\n    let { children } = param;\n    // Enhanced component that properly marks content for Barba\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"page-content\",\n        \"data-barba\": \"container\",\n        \"data-barba-namespace\": \"default\",\n        suppressHydrationWarning: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\PageTransition.jsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PageTransition;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageTransition);\nvar _c;\n$RefreshReg$(_c, \"PageTransition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1BhZ2VUcmFuc2l0aW9uLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUMwQjtBQUUxQixNQUFNQyxpQkFBaUI7UUFBQyxFQUFFQyxRQUFRLEVBQUU7SUFDbEMsMkRBQTJEO0lBQzNELHFCQUNFLDhEQUFDQztRQUNDQyxXQUFVO1FBQ1ZDLGNBQVc7UUFDWEMsd0JBQXFCO1FBQ3JCQyx3QkFBd0I7a0JBRXZCTDs7Ozs7O0FBR1A7S0FaTUQ7QUFjTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXNrdG9wXFxQUkVFVElaRU5cXGNsaWVudFxcc3JjXFxjb21wb25lbnRzXFx1aVxcUGFnZVRyYW5zaXRpb24uanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5jb25zdCBQYWdlVHJhbnNpdGlvbiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICAvLyBFbmhhbmNlZCBjb21wb25lbnQgdGhhdCBwcm9wZXJseSBtYXJrcyBjb250ZW50IGZvciBCYXJiYVxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IFxyXG4gICAgICBjbGFzc05hbWU9XCJwYWdlLWNvbnRlbnRcIiBcclxuICAgICAgZGF0YS1iYXJiYT1cImNvbnRhaW5lclwiIFxyXG4gICAgICBkYXRhLWJhcmJhLW5hbWVzcGFjZT1cImRlZmF1bHRcIlxyXG4gICAgICBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmcgLy8gSW1wb3J0YW50IGZvciBOZXh0LmpzXHJcbiAgICA+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQYWdlVHJhbnNpdGlvbjsiXSwibmFtZXMiOlsiUmVhY3QiLCJQYWdlVHJhbnNpdGlvbiIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YS1iYXJiYSIsImRhdGEtYmFyYmEtbmFtZXNwYWNlIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/PageTransition.jsx\n"));

/***/ })

});