"use client";

import { useEffect, useState, useRef } from "react";

export default function Preloader({ onComplete }) {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(0);
  const preloaderRef = useRef(null);
  const logoRef = useRef(null);
  const progressBarRef = useRef(null);
  const messageRef = useRef(null);
  const percentageRef = useRef(null);

  const loadingMessages = [
    "Woven with care...",
    "Crafting your experience...",
    "Loading, the slow way...",
    "Sustainable fashion awaits..."
  ];

  useEffect(() => {
    const initPreloader = async () => {
      // Dynamic import GSAP
      const gsap = (await import("gsap")).default;

      // Simulate loading progress with realistic curve
      const progressInterval = setInterval(() => {
        setLoadingProgress(prev => {
          if (prev >= 100) {
            clearInterval(progressInterval);
            return 100;
          }
          // Realistic loading curve - faster at start, slower at end
          let increment;
          if (prev < 30) {
            increment = Math.random() * 12 + 5; // Fast initial load
          } else if (prev < 70) {
            increment = Math.random() * 6 + 3; // Medium speed
          } else if (prev < 90) {
            increment = Math.random() * 3 + 1; // Slower near end
          } else {
            increment = Math.random() * 1 + 0.5; // Very slow final stretch
          }
          return Math.min(prev + increment, 100);
        });
      }, 120); // Slightly slower interval for more realistic feel

      // Initial setup - hide elements
      gsap.set([logoRef.current, progressBarRef.current, messageRef.current, percentageRef.current], {
        opacity: 0,
        y: 20
      });

      // Animate in sequence
      const tl = gsap.timeline();

      // 1. Fade in the logo letters with stagger
      const logoLetters = logoRef.current?.querySelectorAll('.logo-letter');
      if (logoLetters) {
        tl.to(logoLetters, {
          opacity: 1,
          y: 0,
          duration: 0.6,
          stagger: 0.08,
          ease: "power2.out"
        }, 0.3);
      }

      // 2. Show progress bar and message
      tl.to([progressBarRef.current, messageRef.current], {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: "power2.out"
      }, 0.8)
      
      // 3. Show percentage counter
      .to(percentageRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.5,
        ease: "power2.out"
      }, 1.0)

      // 4. Add subtle pulse to logo
      .to(logoRef.current, {
        scale: 1.02,
        duration: 2,
        ease: "power1.inOut",
        repeat: -1,
        yoyo: true
      }, 1.5);

      // Rotate loading messages
      const messageInterval = setInterval(() => {
        setCurrentMessage(prev => (prev + 1) % loadingMessages.length);
      }, 1200);

      // Wait for loading to complete
      const checkComplete = setInterval(() => {
        if (loadingProgress >= 100) {
          clearInterval(checkComplete);
          clearInterval(messageInterval);

          // Exit animation
          setTimeout(() => {
            // Check if we're still mounted and refs are valid
            if (!preloaderRef.current) {
              // If already unmounting, just call the completion callback
              setIsLoading(false);
              onComplete?.();
              return;
            }

            const exitTl = gsap.timeline({
              onComplete: () => {
                setIsLoading(false);
                onComplete?.();
              }
            });

            // Filter out any null refs to avoid GSAP errors
            const validElements = [
              logoRef.current, 
              progressBarRef.current, 
              messageRef.current, 
              percentageRef.current
            ].filter(Boolean);

            // Only animate if we have valid elements
            if (validElements.length > 0) {
              exitTl.to(validElements, {
                opacity: 0,
                y: -20,
                duration: 0.6,
                stagger: 0.1,
                ease: "power2.in"
              });
            }

            // Always animate the container if it's available
            if (preloaderRef.current) {
              exitTl.to(preloaderRef.current, {
                opacity: 0,
                duration: 0.8,
                ease: "power2.inOut"
              }, validElements.length > 0 ? "-=0.3" : 0);
            }
          }, 600); // Brief pause at 100%
        }
      }, 100);

      // Cleanup
      return () => {
        clearInterval(progressInterval);
        clearInterval(messageInterval);
        clearInterval(checkComplete);
      };
    };

    initPreloader();
  }, [loadingProgress, onComplete]);

  // Update progress bar width
  useEffect(() => {
    if (progressBarRef.current) {
      const progressFill = progressBarRef.current.querySelector('.progress-fill');
      if (progressFill) {
        progressFill.style.width = `${loadingProgress}%`;
      }
    }
  }, [loadingProgress]);

  if (!isLoading) return null;

  return (
    <div 
      ref={preloaderRef}
      className="fixed inset-0 z-[10000] flex items-center justify-center"
      style={{ backgroundColor: '#FAF9F6' }}
    >
      {/* Main Content Container */}
      <div className="flex flex-col items-center space-y-8">
        
        {/* Brand Logo */}
        <div 
          ref={logoRef}
          className="flex items-center space-x-1"
          style={{ fontFamily: '"Playfair Display", Georgia, serif' }}
        >
          {'PREETIZEN'.split('').map((letter, index) => (
            <span
              key={index}
              className="logo-letter text-4xl md:text-5xl font-bold tracking-wider"
              style={{ 
                color: '#8C644B',
                opacity: 0,
                transform: 'translateY(20px)'
              }}
            >
              {letter}
            </span>
          ))}
        </div>

        {/* Progress Bar Container */}
        <div 
          ref={progressBarRef}
          className="w-64 md:w-80"
          style={{ opacity: 0, transform: 'translateY(20px)' }}
        >
          {/* Progress Bar */}
          <div 
            className="h-0.5 rounded-full overflow-hidden"
            style={{ backgroundColor: '#EAE4DC' }}
          >
            <div 
              className="progress-fill h-full rounded-full transition-all duration-300 ease-out"
              style={{ 
                backgroundColor: '#8C644B',
                width: '0%'
              }}
            />
          </div>
          
          {/* Percentage */}
          <div 
            ref={percentageRef}
            className="text-center mt-3 text-sm font-medium tracking-wide"
            style={{ 
              color: '#8C644B',
              opacity: 0,
              transform: 'translateY(20px)'
            }}
          >
            {Math.round(loadingProgress)}%
          </div>
        </div>

        {/* Loading Message */}
        <div
          ref={messageRef}
          className="text-center h-6 flex items-center justify-center"
          style={{
            opacity: 0,
            transform: 'translateY(20px)'
          }}
        >
          <p
            className="text-sm md:text-base font-light tracking-wide transition-all duration-500 ease-in-out"
            style={{ color: '#1A1A1A' }}
            key={currentMessage}
          >
            {loadingMessages[currentMessage]}
          </p>
        </div>
      </div>

      {/* Subtle Background Pattern */}
      <div 
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #8C644B 1px, transparent 1px)`,
          backgroundSize: '50px 50px'
        }}
      />
    </div>
  );
}
