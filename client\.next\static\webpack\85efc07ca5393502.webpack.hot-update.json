{"c": ["app/layout", "app/join-us/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5CPREETIZEN%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cjoin-us%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/react-icons/md/index.mjs", "(app-pages-browser)/./src/app/join-us/page.js", "(app-pages-browser)/./src/components/join-us/ApplicationFormModal.jsx", "(app-pages-browser)/./src/components/join-us/BenefitsSection.jsx", "(app-pages-browser)/./src/components/join-us/CollaborationsSection.jsx", "(app-pages-browser)/./src/components/join-us/FaqSection.jsx", "(app-pages-browser)/./src/components/join-us/HeroSection.jsx", "(app-pages-browser)/./src/components/join-us/InternshipRoles.jsx", "(app-pages-browser)/./src/components/join-us/RoleCard.jsx", "(app-pages-browser)/./src/components/join-us/TestimonialsSection.jsx", "(app-pages-browser)/./src/data/internshipRoles.js"]}