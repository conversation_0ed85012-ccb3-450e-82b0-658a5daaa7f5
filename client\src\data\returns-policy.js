export const processSteps = [
  {
    title: "Contact Us",
    description: "Email <EMAIL> within 7 days of delivery. Include your order ID, product name, and reason for return. If applicable, attach photos or videos showing the issue.",
    note: "We aim to respond to all return requests within 24-48 hours."
  },
  {
    title: "Await Approval",
    description: "Our team will review your return request and send an approval email with return instructions.",
    note: "Not all return requests may be approved. We evaluate each case based on our return criteria."
  },
  {
    title: "Ship the Item Back",
    description: "Package the item securely with all original tags and packaging. Ship it to our returns address and keep the courier receipt.",
    note: "We recommend using a trackable shipping method."
  },
  {
    title: "Quality Inspection",
    description: "Once we receive your return, our team will inspect the item to ensure it meets our return criteria.",
  },
  {
    title: "Receive Your Refund",
    description: "After inspection, we'll process your refund to your original payment method or issue store credit as per your preference.",
    note: "Refunds typically take 7-10 business days to appear in your account."
  }
];

export const eligibilityItems = [
  "Damaged or defective items",
  "Incorrect item or size delivered",
  "Size issues due to personal preference",
  "Unused, unwashed items with original packaging and tags intact",
  "Return request emailed within 7 days of delivery"
];

export const nonEligibleItems = [
  "Color variation due to screen differences",
  "Customized or made-to-order items",
  "Items without original packaging or tags",
  "Items showing signs of wear or use",
  "Return requests after the 7-day window"
];