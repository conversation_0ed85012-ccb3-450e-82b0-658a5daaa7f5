"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/policies/terms/page",{

/***/ "(app-pages-browser)/./src/app/policies/terms/page.js":
/*!****************************************!*\
  !*** ./src/app/policies/terms/page.js ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TermsAndConditions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_policies_common_PolicyHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/policies/common/PolicyHeader */ \"(app-pages-browser)/./src/components/policies/common/PolicyHeader.jsx\");\n/* harmony import */ var _components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/policies/terms/TermsSection */ \"(app-pages-browser)/./src/components/policies/terms/TermsSection.jsx\");\n/* harmony import */ var _components_policies_terms_Signature__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/policies/terms/Signature */ \"(app-pages-browser)/./src/components/policies/terms/Signature.jsx\");\n/* harmony import */ var _components_policies_terms_ContactInfo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/policies/terms/ContactInfo */ \"(app-pages-browser)/./src/components/policies/terms/ContactInfo.jsx\");\n/* harmony import */ var _components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/PageTransition */ \"(app-pages-browser)/./src/components/ui/PageTransition.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction TermsAndConditions() {\n    const container = {\n        hidden: {\n            opacity: 0\n        },\n        show: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.15,\n                delayChildren: 0.3\n            }\n        }\n    };\n    const item = {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        show: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.7\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_common_PolicyHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    title: \"Terms & Conditions\",\n                    description: \"Please read these terms carefully before using our services\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-[#FFFBF7]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.main, {\n                        className: \"container mx-auto py-12 px-4 md:px-6\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"max-w-4xl mx-auto\",\n                            variants: container,\n                            initial: \"hidden\",\n                            animate: \"show\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-10 pb-6 border-b border-slate-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 md:mb-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-slate-500\",\n                                                    children: \"Effective Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-[var(--heading)]\",\n                                                    children: \"March 5, 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-4 py-2 bg-[var(--accent)]/10 bg-opacity-10 rounded-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-[var(--accent)]\",\n                                                children: \"Legal Document\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: \"Welcome to Preetizen. By accessing or using our website, you agree to be bound by these Terms and Conditions. Please read them carefully before using our services.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 68,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"1\",\n                                        title: \"Basic Agreement & Scope\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"By accessing or purchasing from preetizen.com, you acknowledge that you have read, understood, and agree to be bound by these Terms & Conditions. These terms govern your use of our website and the purchase of products offered by Preetizen Lifestyle.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-4\",\n                                                children: \"All prices displayed on our website include applicable taxes unless otherwise specified.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"2\",\n                                        title: \"Product & Purchase Terms\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Product images on our website are for illustrative purposes only. Actual colors may vary due to differences in screen settings, lighting conditions, and the natural properties of materials used.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-4\",\n                                                children: \"Orders are considered confirmed only after full payment is received. We reserve the right to cancel any order at our discretion before shipping.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-slate-100 p-4 rounded-md mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-slate-800 mb-2\",\n                                                        children: \"Product Quality Commitment\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 text-sm\",\n                                                        children: \"Our products are made with care, and we hope you feel that in every thread. While we strive for perfection, handcrafted items may have slight variations that make each piece unique.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"3\",\n                                        title: \"Pricing & Availability\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Preetizen reserves the right to change pricing or product availability without prior notice. In case a product is listed at an incorrect price due to a typographical error, we have the right to refuse or cancel orders placed for that product.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 123,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-4\",\n                                                children: \"We make every effort to maintain accurate inventory, but occasionally items may be out of stock. In such cases, we will notify you promptly and offer alternatives, store credit, or a refund.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"4\",\n                                        title: \"Account Responsibility\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"If you create an account on our website, you are responsible for maintaining the confidentiality of your account details, including your username and password. You agree to accept responsibility for all activities that occur under your account.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-[var(--primary)]/10 bg-opacity-5 p-4 rounded-md mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-slate-800 mb-2\",\n                                                        children: \"Security Recommendation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"list-disc list-inside text-slate-600 text-sm space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Use a strong, unique password for your Preetizen account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Never share your login credentials with others\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                                lineNumber: 157,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Log out from your account when using shared devices\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                                lineNumber: 158,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"Contact us immediately if you suspect unauthorized access to your account\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 149,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"5\",\n                                        title: \"Intellectual Property\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"All content on our website, including but not limited to text, graphics, logos, images, audio clips, digital downloads, and data compilations, is the property of Preetizen Lifestyle or its content suppliers and is protected by international copyright laws.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-4\",\n                                                children: \"The Preetizen name, logo, and all related names, logos, product and service names, designs, and slogans are trademarks of Preetizen Lifestyle. You may not use these marks without our prior written permission.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 180,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"6\",\n                                        title: \"Legal Entity & Financial Accountability\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Preetizen.com and all associated transactions are legally operated by Preetizen Lifestyle, a registered partnership in India. Navin Mundra is identified as the authorized representative for receiving payments.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-4\",\n                                                children: \"All financial transactions are processed through secure payment gateways. We do not store your complete payment information on our servers.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"7\",\n                                        title: \"Limitation of Liability\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"To the fullest extent permitted by applicable law, Preetizen Lifestyle shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including lost profits, arising out of or in any way connected with your use of our website or products.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"8\",\n                                        title: \"Governing Law\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"These Terms and Conditions shall be governed by and construed in accordance with the laws of India. Any disputes relating to these terms shall be subject to the exclusive jurisdiction of the courts of Mumbai, Maharashtra.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 224,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"9\",\n                                        title: \"Changes to Terms\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"We reserve the right to modify these Terms and Conditions at any time. Changes will be effective immediately upon posting on our website. Your continued use of the website after any such changes constitutes your acceptance of the new Terms and Conditions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 235,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_TermsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        number: \"10\",\n                                        title: \"Contact Information\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"If you have any questions or concerns regarding these Terms and Conditions, please contact our support team at:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 250,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_ContactInfo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                email: \"<EMAIL>\",\n                                                subject: \"Terms and Conditions Inquiry\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 249,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    variants: item,\n                                    className: \"mt-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_policies_terms_Signature__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        companyName: \"Preetizen Lifestyle\",\n                                        representative: \"Navin Mundra\",\n                                        designation: \"Authorized Representative\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\app\\\\policies\\\\terms\\\\page.js\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_c = TermsAndConditions;\nvar _c;\n$RefreshReg$(_c, \"TermsAndConditions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/policies/terms/page.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/PageTransition.jsx":
/*!**********************************************!*\
  !*** ./src/components/ui/PageTransition.jsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst PageTransition = (param)=>{\n    let { children } = param;\n    // Enhanced component that properly marks content for Barba\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"page-content\",\n        \"data-barba\": \"container\",\n        \"data-barba-namespace\": \"default\",\n        suppressHydrationWarning: true,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\PREETIZEN\\\\client\\\\src\\\\components\\\\ui\\\\PageTransition.jsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, undefined);\n};\n_c = PageTransition;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PageTransition);\nvar _c;\n$RefreshReg$(_c, \"PageTransition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1BhZ2VUcmFuc2l0aW9uLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUMwQjtBQUUxQixNQUFNQyxpQkFBaUI7UUFBQyxFQUFFQyxRQUFRLEVBQUU7SUFDbEMsMkRBQTJEO0lBQzNELHFCQUNFLDhEQUFDQztRQUNDQyxXQUFVO1FBQ1ZDLGNBQVc7UUFDWEMsd0JBQXFCO1FBQ3JCQyx3QkFBd0I7a0JBRXZCTDs7Ozs7O0FBR1A7S0FaTUQ7QUFjTixpRUFBZUEsY0FBY0EsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEZXNrdG9wXFxQUkVFVElaRU5cXGNsaWVudFxcc3JjXFxjb21wb25lbnRzXFx1aVxcUGFnZVRyYW5zaXRpb24uanN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5jb25zdCBQYWdlVHJhbnNpdGlvbiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcclxuICAvLyBFbmhhbmNlZCBjb21wb25lbnQgdGhhdCBwcm9wZXJseSBtYXJrcyBjb250ZW50IGZvciBCYXJiYVxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IFxyXG4gICAgICBjbGFzc05hbWU9XCJwYWdlLWNvbnRlbnRcIiBcclxuICAgICAgZGF0YS1iYXJiYT1cImNvbnRhaW5lclwiIFxyXG4gICAgICBkYXRhLWJhcmJhLW5hbWVzcGFjZT1cImRlZmF1bHRcIlxyXG4gICAgICBzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmcgLy8gSW1wb3J0YW50IGZvciBOZXh0LmpzXHJcbiAgICA+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQYWdlVHJhbnNpdGlvbjsiXSwibmFtZXMiOlsiUmVhY3QiLCJQYWdlVHJhbnNpdGlvbiIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwiZGF0YS1iYXJiYSIsImRhdGEtYmFyYmEtbmFtZXNwYWNlIiwic3VwcHJlc3NIeWRyYXRpb25XYXJuaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/PageTransition.jsx\n"));

/***/ })

});