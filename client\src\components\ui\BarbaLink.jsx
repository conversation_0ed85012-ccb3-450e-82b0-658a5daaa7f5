"use client";

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

// This component marks links that should use Barba transitions
export default function BarbaLink({ href, children, className, ...props }) {
  const router = useRouter();

  const handleClick = (e) => {
    e.preventDefault();
    
    // Dispatch a custom event that our Barba wrapper will listen for
    const barbaEvent = new CustomEvent('barba:click', { 
      detail: { href } 
    });
    document.dispatchEvent(barbaEvent);
    
    // Delay the router push to allow animation to start
    setTimeout(() => {
      router.push(href);
    }, 50);
  };

  return (
    <Link 
      href={href}
      onClick={handleClick}
      className={className}
      data-barba-link="true"
      {...props}
    >
      {children}
    </Link>
  );
}